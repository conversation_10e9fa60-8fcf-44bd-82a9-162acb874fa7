# Guía de Desarrollo - Frontend

## Comandos Disponibles

### Desarrollo Normal
```bash
npm run dev:safe
```
- Inicia el servidor en puerto 3001 (puerto seguro)
- Configuración optimizada para Windows
- **Recomendado para uso diario**

### Desarrollo con Limpieza
```bash
npm run dev:clean
```
- Limpia la carpeta .next antes de iniciar
- Usar solo si hay problemas de caché

### Desarrollo Forzado
```bash
npm run dev:force
```
- Reinstala dependencias y limpia todo
- **Solo usar en casos extremos**

## Solución de Problemas Comunes

### Si el servidor se queda en "Starting..."
1. Usar `npm run dev:safe` en lugar de `npm run dev`
2. Si persiste, usar `npm run dev:clean`

### Si hay errores de permisos
1. Cerrar todas las ventanas del navegador
2. Ejecutar: `npm run dev:force`

### Si el puerto está ocupado
- El comando `dev:safe` usa puerto 3001 automáticamente
- Para puerto específico: `npx next dev --port 3002`

## Configuración Aplicada

- ✅ Configuración optimizada para Windows
- ✅ Limpieza automática de caché
- ✅ Manejo de permisos mejorado
- ✅ Puertos alternativos configurados
- ✅ Scripts de limpieza automática

## URLs de Desarrollo
- Puerto principal: http://localhost:3000
- Puerto seguro: http://localhost:3001
- Puerto alternativo: http://localhost:3002

## Notas Importantes
- Siempre usar `npm run dev:safe` para evitar problemas
- Los errores de trace son normales y no afectan el funcionamiento
- La configuración está optimizada para Windows con OneDrive
