# Script para limpiar y ejecutar Next.js en Windows
Write-Host "Limpiando archivos de Next.js..." -ForegroundColor Yellow

# Detener procesos de Node.js que puedan estar usando los archivos
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Esperar un momento para que los procesos se cierren
Start-Sleep -Seconds 2

# Limpiar carpeta .next
if (Test-Path ".next") {
    Write-Host "Eliminando carpeta .next..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force ".next" -ErrorAction SilentlyContinue
}

# Limpiar caché de npm
Write-Host "Limpiando caché de npm..." -ForegroundColor Yellow
npm cache clean --force

Write-Host "Iniciando servidor de desarrollo..." -ForegroundColor Green
npm run dev
